# NewOcrFiledConfig 日志输出示例

## 日志级别说明

- **INFO级别**: 记录方法的开始、结束以及关键的查询结果
- **DEBUG级别**: 记录详细的查询过程，包括具体的查询参数

## 日志输出示例

### 1. getFieldListByCityWithFallback 方法日志示例

#### 场景1：城市维度有配置，国家维度也有配置
```
2025-06-16 10:30:01.123 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback开始执行，入参: cityId=1001, sceneList=[scene1, scene2]
2025-06-16 10:30:01.124 DEBUG [NewOcrFiledConfig] getFieldListByCityWithFallback开始查询城市维度配置，cityId=1001
2025-06-16 10:30:01.125 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback城市维度查询成功，cityId=1001, 查询到字段数量=3
2025-06-16 10:30:01.126 DEBUG [NewOcrFiledConfig] getFieldListByCityWithFallback开始查询国家维度配置，cityId=1001, countryId=86
2025-06-16 10:30:01.127 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback国家维度查询成功，cityId=1001, countryId=86, 查询到字段数量=2
2025-06-16 10:30:01.128 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback执行完成，cityId=1001, 最终返回字段数量=5
```

#### 场景2：城市维度无配置，国家维度有配置
```
2025-06-16 10:30:02.123 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback开始执行，入参: cityId=2001, sceneList=[scene1]
2025-06-16 10:30:02.124 DEBUG [NewOcrFiledConfig] getFieldListByCityWithFallback开始查询城市维度配置，cityId=2001
2025-06-16 10:30:02.125 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback城市维度未查询到配置，cityId=2001
2025-06-16 10:30:02.126 DEBUG [NewOcrFiledConfig] getFieldListByCityWithFallback开始查询国家维度配置，cityId=2001, countryId=81
2025-06-16 10:30:02.127 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback国家维度查询成功，cityId=2001, countryId=81, 查询到字段数量=4
2025-06-16 10:30:02.128 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback执行完成，cityId=2001, 最终返回字段数量=4
```

#### 场景3：城市和国家维度都无配置
```
2025-06-16 10:30:03.123 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback开始执行，入参: cityId=3001, sceneList=[scene1]
2025-06-16 10:30:03.124 DEBUG [NewOcrFiledConfig] getFieldListByCityWithFallback开始查询城市维度配置，cityId=3001
2025-06-16 10:30:03.125 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback城市维度未查询到配置，cityId=3001
2025-06-16 10:30:03.126 DEBUG [NewOcrFiledConfig] getFieldListByCityWithFallback开始查询国家维度配置，cityId=3001, countryId=65
2025-06-16 10:30:03.127 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback国家维度未查询到配置，cityId=3001, countryId=65
2025-06-16 10:30:03.128 INFO  [NewOcrFiledConfig] getFieldListByCityWithFallback执行完成，cityId=3001, 最终返回字段数量=0
```

### 2. getOcrRecognitionResultByCityWithFallback 方法日志示例

#### 场景：城市维度无配置，国家维度有配置
```
2025-06-16 10:31:01.123 INFO  [NewOcrFiledConfig] getOcrRecognitionResultByCityWithFallback开始执行，入参: cityId=1001, imgType=driverLicense
2025-06-16 10:31:01.124 DEBUG [NewOcrFiledConfig] getOcrRecognitionResultByCityWithFallback开始查询城市维度配置，cityId=1001, imgType=driverLicense
2025-06-16 10:31:01.125 INFO  [NewOcrFiledConfig] getOcrRecognitionResultByCityWithFallback城市维度未查询到配置，cityId=1001, imgType=driverLicense
2025-06-16 10:31:01.126 DEBUG [NewOcrFiledConfig] getOcrRecognitionResultByCityWithFallback开始查询国家维度配置，cityId=1001, countryId=86, imgType=driverLicense
2025-06-16 10:31:01.127 INFO  [NewOcrFiledConfig] getOcrRecognitionResultByCityWithFallback国家维度查询成功，cityId=1001, countryId=86, imgType=driverLicense
2025-06-16 10:31:01.128 INFO  [NewOcrFiledConfig] getOcrRecognitionResultByCityWithFallback执行完成，cityId=1001, imgType=driverLicense, 返回结果=有配置
```

### 3. getOcrComplianceByCityWithFallback 方法日志示例

#### 场景：城市维度有配置
```
2025-06-16 10:32:01.123 INFO  [NewOcrFiledConfig] getOcrComplianceByCityWithFallback开始执行，入参: cityId=1001
2025-06-16 10:32:01.124 DEBUG [NewOcrFiledConfig] getOcrComplianceByCityWithFallback开始查询城市维度配置，cityId=1001
2025-06-16 10:32:01.125 INFO  [NewOcrFiledConfig] getOcrComplianceByCityWithFallback城市维度查询成功，cityId=1001, complianceType=1
2025-06-16 10:32:01.126 INFO  [NewOcrFiledConfig] getOcrComplianceByCityWithFallback执行完成，cityId=1001, 返回结果=有配置
```

### 4. getNewOcrByCityWithFallback 方法日志示例

#### 场景：城市维度有配置
```
2025-06-16 10:33:01.123 INFO  [NewOcrFiledConfig] getNewOcrByCityWithFallback开始执行，入参: cityId=1001
2025-06-16 10:33:01.124 DEBUG [NewOcrFiledConfig] getNewOcrByCityWithFallback开始查询城市维度配置，cityId=1001
2025-06-16 10:33:01.125 INFO  [NewOcrFiledConfig] getNewOcrByCityWithFallback城市维度查询成功，cityId=1001, ocrFieldList大小=5
2025-06-16 10:33:01.126 INFO  [NewOcrFiledConfig] getNewOcrByCityWithFallback执行完成，cityId=1001, 返回结果=有配置
```

## 日志的作用

通过这些详细的日志，开发人员和运维人员可以：

1. **快速定位问题**: 当某个城市的配置查询失败时，可以通过日志快速确定是城市维度还是国家维度的配置缺失
2. **监控配置使用情况**: 了解哪些城市使用了城市级配置，哪些回退到了国家级配置
3. **性能分析**: 通过时间戳分析查询性能
4. **配置验证**: 验证配置的正确性和完整性
5. **故障排查**: 在出现问题时，能够快速追踪执行路径

## 日志级别配置建议

- **生产环境**: 建议设置为INFO级别，记录关键的查询结果
- **测试环境**: 可以设置为DEBUG级别，记录详细的查询过程
- **开发环境**: 设置为DEBUG级别，便于开发调试
