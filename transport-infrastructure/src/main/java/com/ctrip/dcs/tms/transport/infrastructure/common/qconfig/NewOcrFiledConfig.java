package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.LocationTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NewOCRDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrRecognitionResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RequiredFieldDTO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class NewOcrFiledConfig {

    @Autowired
    private EnumRepository enumRepository;

    // 必填字段与置灰字段
    private static JsonConfig.ParameterizedClass parameterString1 = JsonConfig.ParameterizedClass.of(String.class);// map key的泛型类型
    private static JsonConfig.ParameterizedClass parametervalue1 = JsonConfig.ParameterizedClass.of(List.class, RequiredFieldDTO.class);
    private static JsonConfig.ParameterizedClass parameter1 = JsonConfig.ParameterizedClass.of(Map.class, parameterString1, parametervalue1);// Map<String, Set<String>>
    private static JsonConfig<Map<String, List<RequiredFieldDTO>>> complexTestJsonConfig1 = JsonConfig.get("new.ocr.back.field.json", parameter1);

    public List<RequiredFieldDTO> getFieldList(Long locationId, String locationType, List<String> sceneList) {
        Map<String, List<RequiredFieldDTO>> current = complexTestJsonConfig1.current();

        List<RequiredFieldDTO> list = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(sceneList)) {
            sceneList.forEach(scene -> {
                List<RequiredFieldDTO> requiredFieldDTOList = current.get(scene);
                if (CollectionUtils.isNotEmpty(requiredFieldDTOList)) {
                    List<RequiredFieldDTO> collect = requiredFieldDTOList.stream().filter(item -> item.getLocationId().contains(locationId) && item.getLocationType().equals(locationType)).collect(Collectors.toList());
                    collect.forEach(item -> item.setScene(scene));
                    if (CollectionUtils.isNotEmpty(collect)) {
                        list.addAll(collect);
                    }

                }
            });
        }
        return list;
    }

    JsonConfig<List<OcrRecognitionResultDTO>> config2 = JsonConfig.get("new.ocr.recognition.result.json", JsonConfig.ParameterizedClass.of(List.class, OcrRecognitionResultDTO.class));
    public OcrRecognitionResultDTO getOcrRecognitionResultList(Long locationId, String locationType, String imgType) {
        List<OcrRecognitionResultDTO> current = config2.current();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType) && a.getImgType().equals(imgType)).findFirst().orElse(null);
    }

    // 自动合规的范围
    JsonConfig<List<OcrComplianceDTO>> ocrComplianceConfig = JsonConfig.get("new.ocr.compliance.type.json", JsonConfig.ParameterizedClass.of(List.class, OcrComplianceDTO.class));
    public OcrComplianceDTO getOcrComplianceList(Long locationId, String locationType) {
        List<OcrComplianceDTO> current = ocrComplianceConfig.current();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType)).findFirst().orElse(null);
    }


    JsonConfig<List<NewOCRDTO>> newOCrConfig = JsonConfig.get("new.ocr.use.json", JsonConfig.ParameterizedClass.of(List.class, NewOCRDTO.class));
    public NewOCRDTO getNewOcr(Long locationId, String locationType) {
        List<NewOCRDTO> current = newOCrConfig.current();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType)).findFirst().orElse(null);
    }

    // ========== 使用枚举的新方法 ==========

    /**
     * 获取字段列表（使用枚举）
     * @param locationId 位置ID
     * @param locationType 位置类型枚举
     * @param sceneList 场景列表
     * @return 字段列表
     */
    public List<RequiredFieldDTO> getFieldList(Long locationId, LocationTypeEnum locationType, List<String> sceneList) {
        return getFieldList(locationId, locationType.getCode(), sceneList);
    }

    /**
     * 获取OCR识别结果（使用枚举）
     * @param locationId 位置ID
     * @param locationType 位置类型枚举
     * @param imgType 图片类型
     * @return OCR识别结果
     */
    public OcrRecognitionResultDTO getOcrRecognitionResultList(Long locationId, LocationTypeEnum locationType, String imgType) {
        return getOcrRecognitionResultList(locationId, locationType.getCode(), imgType);
    }

    /**
     * 获取OCR合规列表（使用枚举）
     * @param locationId 位置ID
     * @param locationType 位置类型枚举
     * @return OCR合规DTO
     */
    public OcrComplianceDTO getOcrComplianceList(Long locationId, LocationTypeEnum locationType) {
        return getOcrComplianceList(locationId, locationType.getCode());
    }

    /**
     * 获取新OCR配置（使用枚举）
     * @param locationId 位置ID
     * @param locationType 位置类型枚举
     * @return 新OCR配置
     */
    public NewOCRDTO getNewOcr(Long locationId, LocationTypeEnum locationType) {
        return getNewOcr(locationId, locationType.getCode());
    }

    // ========== 收口查询方法：先查城市再查国家 ==========

    /**
     * 获取字段列表（先查城市再查国家）
     * @param cityId 城市ID
     * @param sceneList 场景列表
     * @return 字段列表
     */
    public List<RequiredFieldDTO> getFieldListByCityWithFallback(Long cityId, List<String> sceneList) {
        List<RequiredFieldDTO> resultList = new ArrayList<>();

        // 先查城市
        List<RequiredFieldDTO> cityFieldList = getFieldList(cityId, LocationTypeEnum.CITY, sceneList);
        if (CollectionUtils.isNotEmpty(cityFieldList)) {
            resultList.addAll(cityFieldList);
        }

        // 再查国家
        Long countryId = enumRepository.getCountryId(cityId);
        List<RequiredFieldDTO> countryFieldList = getFieldList(countryId, LocationTypeEnum.COUNTRY, sceneList);
        if (CollectionUtils.isNotEmpty(countryFieldList)) {
            resultList.addAll(countryFieldList);
        }

        return resultList;
    }

    /**
     * 获取OCR识别结果（先查城市再查国家）
     * @param cityId 城市ID
     * @param imgType 图片类型
     * @return OCR识别结果
     */
    public OcrRecognitionResultDTO getOcrRecognitionResultByCityWithFallback(Long cityId, String imgType) {
        // 先查城市
        OcrRecognitionResultDTO cityResult = getOcrRecognitionResultList(cityId, LocationTypeEnum.CITY, imgType);
        if (Objects.nonNull(cityResult)) {
            return cityResult;
        }

        // 再查国家
        Long countryId = enumRepository.getCountryId(cityId);
        return getOcrRecognitionResultList(countryId, LocationTypeEnum.COUNTRY, imgType);
    }

    /**
     * 获取OCR合规配置（先查城市再查国家）
     * @param cityId 城市ID
     * @return OCR合规DTO
     */
    public OcrComplianceDTO getOcrComplianceByCityWithFallback(Long cityId) {
        // 先查城市
        OcrComplianceDTO cityCompliance = getOcrComplianceList(cityId, LocationTypeEnum.CITY);
        if (Objects.nonNull(cityCompliance)) {
            return cityCompliance;
        }

        // 再查国家
        Long countryId = enumRepository.getCountryId(cityId);
        return getOcrComplianceList(countryId, LocationTypeEnum.COUNTRY);
    }

    /**
     * 获取新OCR配置（先查城市再查国家）
     * @param cityId 城市ID
     * @return 新OCR配置
     */
    public NewOCRDTO getNewOcrByCityWithFallback(Long cityId) {
        // 先查城市
        NewOCRDTO cityOcr = getNewOcr(cityId, LocationTypeEnum.CITY);
        if (Objects.nonNull(cityOcr)) {
            return cityOcr;
        }

        // 再查国家
        Long countryId = enumRepository.getCountryId(cityId);
        return getNewOcr(countryId, LocationTypeEnum.COUNTRY);
    }
}
