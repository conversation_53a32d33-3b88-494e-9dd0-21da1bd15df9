package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.LocationTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NewOCRDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrRecognitionResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RequiredFieldDTO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * NewOcrFiledConfig 测试类
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RunWith(MockitoJUnitRunner.class)
public class NewOcrFiledConfigTest {

    @Spy
    @InjectMocks
    private NewOcrFiledConfig newOcrFiledConfig;

    @Mock
    private EnumRepository enumRepository;

    @Before
    public void setUp() {
        // 设置mock行为
        when(enumRepository.getCountryId(1L)).thenReturn(10L);
    }

    @Test
    public void testGetFieldListWithEnum() {
        // 测试使用枚举的getFieldList方法
        Long locationId = 1L;
        List<String> sceneList = Arrays.asList("scene1");
        
        RequiredFieldDTO expectedField = new RequiredFieldDTO();
        expectedField.setFieldName("testField");
        List<RequiredFieldDTO> expectedResult = Arrays.asList(expectedField);
        
        // Mock原始方法
        doReturn(expectedResult).when(newOcrFiledConfig)
                .getFieldList(locationId, "city", sceneList);
        
        List<RequiredFieldDTO> result = newOcrFiledConfig.getFieldList(locationId, LocationTypeEnum.CITY, sceneList);
        
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(newOcrFiledConfig).getFieldList(locationId, "city", sceneList);
    }

    @Test
    public void testGetOcrRecognitionResultListWithEnum() {
        // 测试使用枚举的getOcrRecognitionResultList方法
        Long locationId = 1L;
        String imgType = "testImg";
        
        OcrRecognitionResultDTO expectedResult = new OcrRecognitionResultDTO();
        expectedResult.setLocationId(locationId);
        
        // Mock原始方法
        doReturn(expectedResult).when(newOcrFiledConfig)
                .getOcrRecognitionResultList(locationId, "country", imgType);
        
        OcrRecognitionResultDTO result = newOcrFiledConfig.getOcrRecognitionResultList(locationId, LocationTypeEnum.COUNTRY, imgType);
        
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(newOcrFiledConfig).getOcrRecognitionResultList(locationId, "country", imgType);
    }

    @Test
    public void testGetOcrComplianceListWithEnum() {
        // 测试使用枚举的getOcrComplianceList方法
        Long locationId = 1L;
        
        OcrComplianceDTO expectedResult = new OcrComplianceDTO();
        expectedResult.setLocationId(locationId);
        
        // Mock原始方法
        doReturn(expectedResult).when(newOcrFiledConfig)
                .getOcrComplianceList(locationId, "city");
        
        OcrComplianceDTO result = newOcrFiledConfig.getOcrComplianceList(locationId, LocationTypeEnum.CITY);
        
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(newOcrFiledConfig).getOcrComplianceList(locationId, "city");
    }

    @Test
    public void testGetNewOcrWithEnum() {
        // 测试使用枚举的getNewOcr方法
        Long locationId = 1L;
        
        NewOCRDTO expectedResult = new NewOCRDTO();
        expectedResult.setLocationId(locationId);
        
        // Mock原始方法
        doReturn(expectedResult).when(newOcrFiledConfig)
                .getNewOcr(locationId, "country");
        
        NewOCRDTO result = newOcrFiledConfig.getNewOcr(locationId, LocationTypeEnum.COUNTRY);
        
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(newOcrFiledConfig).getNewOcr(locationId, "country");
    }

    @Test
    public void testGetFieldListByCityWithFallback_CityFound() {
        // 测试收口方法：城市找到的情况
        Long cityId = 1L;
        List<String> sceneList = Arrays.asList("scene1");
        
        RequiredFieldDTO cityField = new RequiredFieldDTO();
        cityField.setFieldName("cityField");
        List<RequiredFieldDTO> cityResult = Arrays.asList(cityField);
        
        // Mock城市查询返回结果
        doReturn(cityResult).when(newOcrFiledConfig)
                .getFieldList(cityId, LocationTypeEnum.CITY, sceneList);
        doReturn(Arrays.asList()).when(newOcrFiledConfig)
                .getFieldList(10L, LocationTypeEnum.COUNTRY, sceneList);
        
        List<RequiredFieldDTO> result = newOcrFiledConfig.getFieldListByCityWithFallback(cityId, sceneList);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("cityField", result.get(0).getFieldName());
        
        verify(newOcrFiledConfig).getFieldList(cityId, LocationTypeEnum.CITY, sceneList);
        verify(newOcrFiledConfig).getFieldList(10L, LocationTypeEnum.COUNTRY, sceneList);
        verify(enumRepository).getCountryId(cityId);
    }

    @Test
    public void testGetOcrRecognitionResultByCityWithFallback_FallbackToCountry() {
        // 测试收口方法：回退到国家的情况
        Long cityId = 1L;
        String imgType = "testImg";
        
        OcrRecognitionResultDTO countryResult = new OcrRecognitionResultDTO();
        countryResult.setLocationId(10L);
        
        // Mock城市查询返回null，国家查询返回结果
        doReturn(null).when(newOcrFiledConfig)
                .getOcrRecognitionResultList(cityId, LocationTypeEnum.CITY, imgType);
        doReturn(countryResult).when(newOcrFiledConfig)
                .getOcrRecognitionResultList(10L, LocationTypeEnum.COUNTRY, imgType);
        
        OcrRecognitionResultDTO result = newOcrFiledConfig.getOcrRecognitionResultByCityWithFallback(cityId, imgType);
        
        assertNotNull(result);
        assertEquals(countryResult, result);
        
        verify(newOcrFiledConfig).getOcrRecognitionResultList(cityId, LocationTypeEnum.CITY, imgType);
        verify(newOcrFiledConfig).getOcrRecognitionResultList(10L, LocationTypeEnum.COUNTRY, imgType);
        verify(enumRepository).getCountryId(cityId);
    }

    @Test
    public void testGetOcrComplianceByCityWithFallback_CityFound() {
        // 测试收口方法：城市找到的情况
        Long cityId = 1L;
        
        OcrComplianceDTO cityResult = new OcrComplianceDTO();
        cityResult.setLocationId(cityId);
        
        // Mock城市查询返回结果
        doReturn(cityResult).when(newOcrFiledConfig)
                .getOcrComplianceList(cityId, LocationTypeEnum.CITY);
        
        OcrComplianceDTO result = newOcrFiledConfig.getOcrComplianceByCityWithFallback(cityId);
        
        assertNotNull(result);
        assertEquals(cityResult, result);
        
        verify(newOcrFiledConfig).getOcrComplianceList(cityId, LocationTypeEnum.CITY);
        // 不应该查询国家，因为城市已找到
        verify(newOcrFiledConfig, never()).getOcrComplianceList(10L, LocationTypeEnum.COUNTRY);
        verify(enumRepository, never()).getCountryId(cityId);
    }

    @Test
    public void testGetNewOcrByCityWithFallback_BothNotFound() {
        // 测试收口方法：城市和国家都没找到的情况
        Long cityId = 1L;

        // Mock城市和国家查询都返回null
        doReturn(null).when(newOcrFiledConfig)
                .getNewOcr(cityId, LocationTypeEnum.CITY);
        doReturn(null).when(newOcrFiledConfig)
                .getNewOcr(10L, LocationTypeEnum.COUNTRY);

        NewOCRDTO result = newOcrFiledConfig.getNewOcrByCityWithFallback(cityId);

        assertNull(result);

        verify(newOcrFiledConfig).getNewOcr(cityId, LocationTypeEnum.CITY);
        verify(newOcrFiledConfig).getNewOcr(10L, LocationTypeEnum.COUNTRY);
        verify(enumRepository).getCountryId(cityId);
    }

    @Test
    public void testLoggingBehavior() {
        // 测试日志行为：验证方法执行时会输出相应的日志
        Long cityId = 1L;
        List<String> sceneList = Arrays.asList("scene1");

        RequiredFieldDTO cityField = new RequiredFieldDTO();
        cityField.setFieldName("cityField");
        List<RequiredFieldDTO> cityResult = Arrays.asList(cityField);

        // Mock城市查询返回结果，国家查询返回空
        doReturn(cityResult).when(newOcrFiledConfig)
                .getFieldList(cityId, LocationTypeEnum.CITY, sceneList);
        doReturn(Arrays.asList()).when(newOcrFiledConfig)
                .getFieldList(10L, LocationTypeEnum.COUNTRY, sceneList);

        // 执行方法
        List<RequiredFieldDTO> result = newOcrFiledConfig.getFieldListByCityWithFallback(cityId, sceneList);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("cityField", result.get(0).getFieldName());

        // 验证方法调用
        verify(newOcrFiledConfig).getFieldList(cityId, LocationTypeEnum.CITY, sceneList);
        verify(newOcrFiledConfig).getFieldList(10L, LocationTypeEnum.COUNTRY, sceneList);
        verify(enumRepository).getCountryId(cityId);

        // 注意：在实际的单元测试中，如果需要验证日志输出，
        // 可以使用LogCaptor或类似的工具来捕获和验证日志内容
        // 这里我们主要验证方法的逻辑正确性
    }
}
